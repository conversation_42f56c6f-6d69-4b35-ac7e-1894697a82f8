<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="4969px" height="1388px" viewBox="0 0 4969.420024999999 1387.9061091108579">
    <g style="" id="088a79aa-053e-42bd-9123-3f818896fee4">
<g style=""><path d="M-1183.42577 -60.54235C-1215.09159 -55.82617 -1231.26137 -78.05961 -1240.69374 -109.72543C-1260.23222 -173.05707 -1284.48689 -420.99369 -1261.5797 -542.94078C-1250.12611 -604.2512 -1196.90058 -635.91702 -1153.10743 -626.48465C-1091.12327 -614.35731 -1099.8819 -561.80553 -1109.31428 -511.94871C-1131.54772 -383.93795 -1137.61139 -255.25345 -1135.59017 -113.76788C-1135.59017 -86.1445 -1143.67506 -67.27976 -1183.42577 -60.54235Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M-390.43281 -22.81286C-403.90763 -22.81286 -419.40367 -30.22401 -434.89971 -49.7625C-473.30293 -98.27184 -489.47271 -205.39663 -494.86264 -272.77071C-570.99535 -272.77071 -617.48347 -269.40201 -691.59496 -260.64338C-690.92122 -217.52396 -685.53129 -174.40455 -682.83633 -127.91643C-679.46763 -95.57687 -705.74352 -78.73335 -729.32445 -82.10206C-781.87623 -89.51321 -795.35105 -157.56103 -798.04601 -259.2959C-812.19457 -269.40201 -816.23702 -284.22431 -816.23702 -294.33042C-816.23702 -303.08905 -813.54205 -311.84768 -808.15213 -318.58509C-814.21579 -377.20054 -818.93198 -558.43682 -813.54205 -583.36523C-806.1309 -617.72602 -769.07516 -646.02313 -731.34567 -641.98069C-704.39604 -639.28572 -663.29785 -613.00983 -663.29785 -582.69149C-663.29785 -579.99653 -663.29785 -577.97531 -663.97159 -575.28034C-684.18381 -489.04152 -692.2687 -416.27751 -690.92122 -346.20846C-618.15721 -354.29335 -579.75398 -357.66206 -505.64249 -357.66206C-506.31623 -414.93003 -509.68494 -462.09189 -509.68494 -501.84259C-509.68494 -550.35193 -504.96875 -586.73394 -484.75653 -613.00983C-467.91301 -633.8958 -441.63711 -648.7181 -413.34 -648.7181C-376.95799 -648.7181 -324.40621 -623.11594 -324.40621 -558.43682C-324.40621 -549.00445 -325.75369 -539.57208 -327.77491 -529.46597C-354.72455 -375.17932 -358.76699 -225.60885 -349.33462 -80.75458C-346.63966 -48.41502 -365.5044 -22.81286 -390.43281 -22.81286Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M122.9577 -62.56357C93.3131 -62.56357 64.34225 -81.42832 64.34225 -111.74665C64.34225 -156.88729 87.92318 -174.40455 83.20699 -218.87145C1.68435 -207.41785 -79.83829 -201.35418 -137.78 -205.39663L-137.78 -183.16318C-137.78 -117.81032 -166.75086 -83.44954 -219.30264 -83.44954C-259.05335 -83.44954 -297.45658 -115.7891 -297.45658 -182.48944C-297.45658 -232.34626 -280.61306 -330.03868 -275.22313 -360.35702C-252.31594 -484.99907 -176.85697 -646.02313 -28.63399 -646.02313C139.12748 -646.02313 199.76416 -426.38362 199.76416 -255.92719C199.76416 -203.37541 194.37423 -153.51859 184.26812 -111.07291C176.18323 -77.38587 149.90733 -62.56357 122.9577 -62.56357ZM-132.39007 -297.02538C-79.83829 -294.33042 19.20161 -299.72035 79.83829 -308.47898C71.7534 -438.51096 29.98147 -518.68612 -17.85413 -518.68612C-76.46958 -518.68612 -126.32641 -433.79477 -132.39007 -297.02538Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M418.05618 -53.1312C386.39037 -53.1312 352.70332 -64.5848 349.33462 -91.53443C347.98714 -102.98802 345.29218 -186.53189 345.29218 -206.07037C345.29218 -298.37286 351.35584 -387.98039 364.15692 -479.60915C348.66088 -476.91418 323.73247 -477.58792 298.13032 -477.58792C232.77746 -477.58792 200.4379 -497.80015 200.4379 -556.4156C200.4379 -598.86127 222.67134 -644.67565 271.85442 -644.67565C278.59183 -644.67565 280.61306 -642.65443 290.71917 -639.95946C339.22851 -623.78968 389.75907 -618.39976 441.63711 -618.39976C508.33746 -618.39976 614.11477 -636.59076 659.92914 -621.09472C688.9 -611.66235 701.02733 -584.03898 701.02733 -554.39438C701.02733 -515.99115 680.14137 -474.89296 649.82303 -471.52426C628.93706 -469.50303 513.05364 -485.67281 494.1889 -485.67281C471.28171 -484.32533 469.26049 -487.69404 469.26049 -468.82929C469.26049 -396.73902 480.71408 -286.91927 491.49394 -209.43907C498.90508 -163.6247 504.96875 -129.26392 506.98997 -115.11536C511.03242 -87.49198 471.28171 -53.1312 418.05618 -53.1312Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M959.74381 -45.04631C858.68269 -47.74128 719.89208 -80.75458 732.01941 -181.14196C738.08308 -241.10489 788.61364 -292.3092 832.4068 -328.6912C775.13882 -356.98832 725.282 -398.76025 725.282 -473.54548C725.282 -590.10264 880.24239 -671.62528 1002.86322 -671.62528C1109.31428 -671.62528 1203.63799 -614.35731 1199.59555 -507.90626C1199.59555 -468.15555 1164.56102 -439.85844 1130.20024 -439.85844C1107.29305 -439.85844 1086.40709 -452.65951 1073.60601 -480.95663C1057.43623 -518.68612 1021.05423 -536.87712 971.19741 -536.87712C907.19203 -536.87712 835.10176 -506.55878 835.10176 -462.76563C835.10176 -420.31995 876.87369 -401.45521 947.61648 -393.37032C965.80748 -391.3491 977.26107 -373.1581 977.93481 -352.94587C978.60856 -323.97502 959.74381 -309.82646 933.46792 -292.98294C884.28484 -261.99086 850.5978 -222.24015 850.5978 -195.96426C850.5978 -158.90851 915.95066 -156.88729 1015.6643 -178.447C1076.97472 -191.92181 1105.27183 -200.0067 1116.05168 -266.0333C1124.13657 -306.45775 1154.45491 -322.62753 1184.77325 -322.62753C1237.32503 -322.62753 1266.29589 -277.4869 1266.29589 -224.26137C1266.29589 -87.49198 1091.12327 -41.00387 959.74381 -45.04631Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M-1115.04107 578.8377C-1202.62738 578.8377 -1216.77594 479.79779 -1216.77594 399.62264C-1216.77594 349.76581 -1198.58494 192.7842 -1199.93242 142.92738C-1199.93242 111.26156 -1207.34357 98.46048 -1222.83961 98.46048C-1272.02269 98.46048 -1330.63814 233.88239 -1352.87159 277.0018C-1364.99892 300.58273 -1384.5374 312.03633 -1409.46582 312.03633C-1435.74171 312.03633 -1454.60645 292.49784 -1466.06005 254.76836C-1473.47119 229.83995 -1482.22983 201.54283 -1486.27227 190.08924C-1497.05212 158.42342 -1503.11579 146.96982 -1513.89564 146.96982C-1532.76039 146.96982 -1544.21398 218.38635 -1544.88772 258.8108C-1546.2352 318.1 -1540.84528 381.43163 -1511.87442 462.95427C-1509.17946 470.36542 -1505.13701 481.81902 -1505.13701 491.92513C-1505.13701 533.69706 -1552.97261 568.73158 -1594.74454 568.73158C-1619.67295 568.73158 -1639.88518 554.58303 -1653.36 533.69706C-1707.25926 448.80572 -1688.39452 215.69139 -1644.60137 114.63026C-1623.7154 66.12093 -1586.65965 10.20044 -1518.61183 10.20044C-1445.84782 10.20044 -1412.16078 92.39682 -1397.33848 161.79212C-1389.25359 142.92738 -1378.47374 121.36767 -1364.99892 97.78674C-1321.20577 20.98029 -1277.41261 -25.50783 -1199.93242 -20.79164C-1135.92704 -16.7492 -1096.17633 17.61159 -1083.37525 78.922C-1075.29036 117.32523 -1072.5954 295.19281 -1068.55296 349.76581C-1064.51051 405.01256 -1061.81555 427.91975 -1038.90836 469.69168C-1032.84469 481.14528 -1028.80225 493.27261 -1028.80225 506.07369C-1028.80225 554.58303 -1073.26914 578.8377 -1115.04107 578.8377Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M-779.51814 578.16396C-918.30875 578.16396 -1017.34865 463.62801 -1017.34865 320.12122C-1017.34865 182.67809 -918.98249 10.87418 -807.81525 10.87418C-799.05662 10.87418 -791.64547 12.22166 -784.90807 14.24288C-757.95843 0.76806 -731.0088 -6.64308 -701.3642 -6.64308C-572.67971 -6.64308 -494.52577 119.34645 -494.52577 255.4421C-494.52577 427.91975 -604.34552 578.16396 -779.51814 578.16396ZM-864.40948 303.2777C-875.18934 391.53775 -833.41741 441.39457 -767.3908 441.39457C-669.69838 441.39457 -611.75667 344.37589 -611.75667 240.6198C-611.75667 162.46586 -639.38005 95.09178 -705.40665 95.09178C-757.95843 95.09178 -804.44655 144.27486 -804.44655 208.28024C-804.44655 245.33599 -779.51814 272.95936 -772.10699 309.34136C-768.06455 330.22733 -775.47569 351.78704 -801.75159 351.1133C-826.00626 350.43956 -854.30337 319.44748 -864.40948 303.2777Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M-16.84352 578.8377C-125.98954 578.8377 -220.98699 425.22479 -269.49633 298.56151C-275.56 283.73921 -278.25496 279.02303 -282.29741 279.69677C-286.33985 280.37051 -286.33985 289.12914 -286.33985 296.54029C-286.33985 356.50322 -274.88626 435.3309 -274.21252 460.25931C-272.1913 529.65462 -292.40352 566.71036 -351.01897 566.71036C-398.18083 566.71036 -433.88909 525.61217 -445.34269 441.39457C-456.12254 363.24063 -454.77506 225.7975 -429.84665 148.31731C-409.63442 90.37559 -370.55746 36.47633 -307.22582 36.47633C-253.32655 36.47633 -220.98699 74.20581 -196.73232 122.71515C-140.81183 234.55613 -152.26543 270.2644 -74.78523 399.62264C-56.59423 429.26723 -38.40323 444.08953 -24.25467 444.08953C-8.08489 444.08953 3.3687 423.87731 6.06367 379.41041C8.08489 360.54567 8.75863 342.35467 8.75863 324.16366C8.75863 250.05217 -6.06367 177.28816 -13.47482 91.04934C-17.51726 45.23496 20.88597 20.98029 64.67912 20.98029C97.69242 20.98029 129.35824 39.84503 135.42191 80.26948C140.13809 109.91408 143.5068 154.38097 143.5068 204.91154C143.5068 339.6597 115.20968 578.8377 -16.84352 578.8377Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M301.16215 586.92259C227.7244 586.92259 192.01614 520.89599 189.32117 453.5219C185.27873 353.80826 175.84636 193.45794 175.84636 92.39682C175.84636 34.45511 181.91002 -5.96934 244.56792 -5.96934C282.29741 -5.96934 307.22582 2.78929 331.48049 27.04396C363.14631 -3.94812 413.67687 -16.07546 460.16499 -18.77042C586.82826 -18.77042 648.81242 67.46841 638.03257 187.39427C623.88401 341.68092 458.81751 586.92259 301.16215 586.92259ZM312.61574 391.53775C312.61574 419.83486 318.00567 430.61471 334.84919 430.61471C414.35061 430.61471 516.75922 257.46332 516.75922 163.81335C516.75922 116.65149 485.0934 74.87956 419.0668 91.04934C310.59452 117.32523 311.942 280.37051 312.61574 391.53775Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M1067.87922 564.0154C1038.23462 564.0154 1009.26376 545.15065 1009.26376 514.83232C1009.26376 469.69168 1032.84469 452.17442 1028.12851 407.70753C946.60587 419.16112 865.08323 425.22479 807.14151 421.18234L807.14151 443.41579C807.14151 508.76865 778.17066 543.12943 725.61887 543.12943C685.86816 543.12943 647.46494 510.78987 647.46494 444.08953C647.46494 394.23271 664.30846 296.54029 669.69838 266.22195C692.60557 141.5799 768.06455 -19.44416 916.28753 -19.44416C1084.04899 -19.44416 1144.68567 200.19535 1144.68567 370.65178C1144.68567 423.20356 1139.29574 473.06039 1129.18963 515.50606C1121.10474 549.1931 1094.82885 564.0154 1067.87922 564.0154ZM812.53144 329.55359C865.08323 332.24855 964.12313 326.85863 1024.7598 318.1C1016.67491 188.06802 974.90298 107.89286 927.06738 107.89286C868.45193 107.89286 818.59511 192.7842 812.53144 329.55359Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/><path d="M1497.72586 605.78733C1424.28811 605.78733 1399.3597 581.53266 1326.59569 581.53266C1284.82376 581.53266 1261.91657 544.47691 1261.91657 506.07369C1261.91657 466.32298 1287.51873 423.87731 1343.43921 423.87731C1395.31726 423.87731 1430.35178 458.91183 1482.90357 458.91183C1505.81075 458.91183 1526.02298 445.43701 1542.19276 400.29638C1558.36254 355.15574 1567.12117 303.2777 1574.53232 256.78958C1505.81075 355.15574 1396.66474 374.69423 1361.63022 374.69423C1319.18454 374.69423 1276.06513 362.56689 1245.74679 330.22733C1204.6486 286.43418 1176.35149 196.82665 1176.35149 120.69393C1176.35149 87.00689 1179.72019 60.05726 1182.41516 45.9087C1191.84753 2.78929 1231.59824 -14.72797 1268.65398 -14.72797C1310.42591 -14.72797 1351.5241 8.85295 1346.13418 47.25618C1344.11296 60.731 1340.74425 86.33315 1340.74425 115.30401C1340.74425 180.65687 1352.87159 263.52699 1404.07589 263.52699C1461.34386 263.52699 1530.06542 168.52953 1557.01506 112.60904C1571.83736 81.61696 1581.94347 60.731 1591.37584 47.92992C1606.19814 27.7177 1625.06288 18.95907 1642.58014 18.95907C1674.9197 18.95907 1704.5643 48.60366 1699.17437 86.33315C1689.06826 142.92738 1682.33085 280.37051 1677.61467 332.24855C1668.85604 473.06039 1638.5377 605.78733 1497.72586 605.78733Z" fill="rgb(255, 239, 58)" transform="matrix(1 0 0 1 2484.710012 693.953055)"/></g></g>

  </svg>