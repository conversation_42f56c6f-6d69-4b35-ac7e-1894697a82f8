<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="987px" height="405px" viewBox="0 0 986.9974278000009 404.6677782000006">
    <g id="06e055b2-f17c-41e1-9081-a6ad3fad07cf">
<g style="">
		<g id="06e055b2-f17c-41e1-9081-a6ad3fad07cf-child-0">
<path style="stroke: rgb(60,232,25); stroke-width: 11.198598; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" d="M5.5993,399.06848v-393.46918h975.79883v393.46918z" stroke-linecap="round"/>
</g>
</g>
</g>
<g id="7821f237-12d1-4bf4-b155-54810db44ac5">
<g style="">
		<g id="7821f237-12d1-4bf4-b155-54810db44ac5-child-0">
<path style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(219,32,32); fill-rule: nonzero; opacity: 1;" transform="matrix(-29.324483 0 0 -18.381623 261.946692 239.827253) matrix(1 0 0 1 0 0)  translate(-7.6365, -7.6365)" d="M 0.00 1.80 L 0.90 1.80 L 0.90 13.47 L 0.00 13.47 L 0.00 1.80 z M 1.45 13.47 L 1.95 13.47 L 1.95 1.80 L 1.45 1.80 L 1.45 13.47 z M 2.31 13.47 L 2.82 13.47 L 2.82 1.80 L 2.31 1.80 L 2.31 13.47 z M 3.48 13.47 L 4.59 13.47 L 4.59 1.80 L 3.48 1.80 L 3.48 13.47 z M 5.47 13.47 L 6.02 13.47 L 6.02 1.80 L 5.47 1.80 L 5.47 13.47 z M 6.88 13.47 L 9.17 13.47 L 9.17 1.80 L 6.88 1.80 L 6.88 13.47 z M 12.84 13.47 L 13.59 13.47 L 13.59 1.80 L 12.84 1.80 L 12.84 13.47 z M 14.25 1.80 L 14.25 13.47 L 15.27 13.47 L 15.27 1.80 L 14.25 1.80 z M 10.08 13.47 L 10.58 13.47 L 10.58 1.80 L 10.08 1.80 L 10.08 13.47 z M 11.28 13.47 L 11.77 13.47 L 11.77 1.80 L 11.28 1.80 L 11.28 13.47 z" stroke-linecap="round"/>
</g>
</g>
</g>
<g id="0583a487-0038-4a85-a84a-07825cc86a0d">
<g style="">
		<g id="0583a487-0038-4a85-a84a-07825cc86a0d-child-0">
<path style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(219,32,32); fill-rule: nonzero; opacity: 1;" transform="matrix(29.324483 0 0 -18.381623 725.050874 239.827249) matrix(1 0 0 1 0 0)  translate(-7.6365, -7.6365)" d="M 0.00 1.80 L 0.90 1.80 L 0.90 13.47 L 0.00 13.47 L 0.00 1.80 z M 1.45 13.47 L 1.95 13.47 L 1.95 1.80 L 1.45 1.80 L 1.45 13.47 z M 2.31 13.47 L 2.82 13.47 L 2.82 1.80 L 2.31 1.80 L 2.31 13.47 z M 3.48 13.47 L 4.59 13.47 L 4.59 1.80 L 3.48 1.80 L 3.48 13.47 z M 5.47 13.47 L 6.02 13.47 L 6.02 1.80 L 5.47 1.80 L 5.47 13.47 z M 6.88 13.47 L 9.17 13.47 L 9.17 1.80 L 6.88 1.80 L 6.88 13.47 z M 12.84 13.47 L 13.59 13.47 L 13.59 1.80 L 12.84 1.80 L 12.84 13.47 z M 14.25 1.80 L 14.25 13.47 L 15.27 13.47 L 15.27 1.80 L 14.25 1.80 z M 10.08 13.47 L 10.58 13.47 L 10.58 1.80 L 10.08 1.80 L 10.08 13.47 z M 11.28 13.47 L 11.77 13.47 L 11.77 1.80 L 11.28 1.80 L 11.28 13.47 z" stroke-linecap="round"/>
</g>
</g>
</g>
<g style="" id="ed313683-d7b7-4dbb-a753-346629dc79fd">
<g style=""><path d="M-431.81271 -5.06706L-407.58714 22.78509L-420.35271 22.78509L-441.31435 -1.65807L-441.31435 22.78509L-451.17865 22.78509L-451.17865 -30.88832L-441.31435 -30.88832L-441.31435 -7.6782L-420.86043 -30.81579L-408.53005 -30.81579Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-388.87785 22.78509L-398.74215 22.78509L-398.74215 -30.88832L-388.87785 -30.88832Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-336.29629 -30.88832L-336.29629 -22.32959L-352.8335 -22.32959L-352.8335 22.78509L-362.6978 22.78509L-362.6978 -22.32959L-379.23501 -22.32959L-379.23501 -30.88832Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-316.78915 -30.88832L-316.78915 14.22635L-290.09751 14.22635L-290.09751 22.78509L-326.65345 22.78509L-326.65345 -30.88832Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-269.13973 -30.88832L-269.13973 14.22635L-242.44809 14.22635L-242.44809 22.78509L-279.00404 22.78509L-279.00404 -30.88832Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-225.04437 23.5104Q-227.51045 23.5104 -229.10614 21.84218Q-230.70184 20.17395 -230.70184 17.7804Q-230.70184 15.38686 -229.10614 13.7549Q-227.51045 12.12294 -225.04437 12.12294Q-222.5783 12.12294 -220.94633 13.7549Q-219.31437 15.38686 -219.31437 17.7804Q-219.31437 20.17395 -220.94633 21.84218Q-222.5783 23.5104 -225.04437 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-186.8966 23.43787Q-193.64204 23.43787 -198.61046 19.73876Q-203.57888 16.03965 -206.15375 9.76566Q-208.72862 3.49167 -208.72862 -4.12415Q-208.72862 -11.73997 -206.15375 -18.01396Q-203.57888 -24.28794 -198.61046 -27.98706Q-193.64204 -31.68617 -186.8966 -31.68617Q-180.15116 -31.68617 -175.18274 -27.98706Q-170.21433 -24.28794 -167.63945 -18.01396Q-165.06458 -11.73997 -165.06458 -4.12415Q-165.06458 3.49167 -167.63945 9.76566Q-170.21433 16.03965 -175.18274 19.73876Q-180.15116 23.43787 -186.8966 23.43787ZM-186.8966 14.66154Q-181.67433 14.66154 -178.44667 9.80192Q-175.21901 4.94231 -175.21901 -4.12415Q-175.21901 -13.11807 -178.44667 -17.94142Q-181.67433 -22.76478 -186.8966 -22.76478Q-192.11888 -22.76478 -195.34654 -17.94142Q-198.5742 -13.11807 -198.5742 -4.12415Q-198.5742 4.94231 -195.3828 9.80192Q-192.19141 14.66154 -186.8966 14.66154Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-137.36137 -30.88832L-137.36137 22.78509L-147.08061 22.78509L-147.08061 -16.67212L-157.30757 -16.67212L-157.30757 -22.54718L-155.63934 -23.0549Q-151.35997 -24.28794 -148.6763 -25.88364Q-145.99263 -27.47933 -143.59909 -30.23554L-142.9463 -30.88832Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-117.27398 23.5104Q-119.74005 23.5104 -121.33575 21.84218Q-122.93144 20.17395 -122.93144 17.7804Q-122.93144 15.38686 -121.33575 13.7549Q-119.74005 12.12294 -117.27398 12.12294Q-114.8079 12.12294 -113.17594 13.7549Q-111.54398 15.38686 -111.54398 17.7804Q-111.54398 20.17395 -113.17594 21.84218Q-114.8079 23.5104 -117.27398 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-81.22962 -30.88832L-81.22962 22.78509L-90.94886 22.78509L-90.94886 -16.67212L-101.17582 -16.67212L-101.17582 -22.54718L-99.5076 -23.0549Q-95.22823 -24.28794 -92.54456 -25.88364Q-89.86089 -27.47933 -87.46734 -30.23554L-86.81456 -30.88832Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M-53.38135 14.15382L-29.22831 14.15382L-29.22831 22.78509L-67.88767 22.78509L-67.88767 15.96711L-57.80578 7.04572L-53.45388 3.34661Q-48.30413 -0.93276 -45.65673 -3.47136Q-43.00932 -6.00997 -41.48616 -8.65738Q-39.96299 -11.30478 -39.96299 -14.27858Q-39.96299 -16.96225 -41.26856 -18.9206Q-42.57413 -20.87896 -44.67755 -21.93066Q-46.78097 -22.98237 -49.10198 -22.98237Q-52.87363 -22.98237 -55.01331 -21.09655Q-57.15299 -19.21073 -57.95084 -16.88972Q-58.74869 -14.5687 -58.74869 -12.97301L-68.1778 -12.97301Q-68.1778 -16.38199 -66.36451 -20.73389Q-64.55122 -25.08579 -60.23559 -28.34971Q-55.91995 -31.61364 -48.88439 -31.61364Q-43.73464 -31.61364 -39.49154 -29.54649Q-35.24844 -27.47933 -32.70983 -23.63516Q-30.17122 -19.79098 -30.17122 -14.71377Q-30.17122 -10.07174 -32.45597 -6.15503Q-34.74072 -2.23833 -38.29477 1.13439Q-41.84882 4.50712 -48.81185 10.09205Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M0.94098 23.5104Q-5.877 23.5104 -10.30143 20.82673Q-14.72586 18.14306 -16.72048 14.22635Q-18.7151 10.30965 -18.7151 6.46547L-9.43105 6.46547Q-9.43105 8.78648 -8.27054 10.81737Q-7.11003 12.84825 -4.75276 14.08129Q-2.39548 15.31433 0.86844 15.31433Q5.14781 15.31433 7.97654 13.06585Q10.80528 10.81737 10.80528 6.97319Q10.80528 3.41914 8.3392 1.27946Q5.87313 -0.86023 1.66629 -0.86023L-5.58687 -0.86023L-5.58687 -8.47605L1.73882 -8.47605Q5.36541 -8.47605 7.68642 -10.50693Q10.00743 -12.53782 10.00743 -15.72921Q10.00743 -19.1382 7.36003 -21.31415Q4.71262 -23.49009 0.86844 -23.49009Q-2.10535 -23.49009 -4.24504 -22.18453Q-6.38472 -20.87896 -7.50896 -18.77554Q-8.6332 -16.67212 -8.6332 -14.35111L-17.69965 -14.35111Q-17.69965 -18.19529 -15.7413 -22.18453Q-13.78295 -26.17376 -9.57611 -28.8937Q-5.36928 -31.61364 1.01351 -31.61364Q5.87313 -31.61364 10.11623 -29.80035Q14.35933 -27.98706 16.97047 -24.57807Q19.58161 -21.16908 19.58161 -16.67212Q19.58161 -12.24769 17.18806 -9.20136Q14.79452 -6.15503 11.45806 -4.99453L11.45806 -4.55934Q15.51983 -3.18124 18.05844 -0.09864Q20.59705 2.98395 20.59705 7.48091Q20.59705 12.41306 17.91338 16.03965Q15.22971 19.66623 10.73275 21.58832Q6.23579 23.5104 0.94098 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M51.34659 23.5104Q44.09343 23.5104 39.669 20.42781Q35.24457 17.34521 33.39501 13.42851Q31.54546 9.5118 31.47292 6.538L40.68444 6.538Q41.11963 10.23711 43.73077 12.81199Q46.34191 15.38686 51.20153 15.38686Q54.61051 15.38686 57.04032 13.89996Q59.47013 12.41306 60.66691 10.01952Q61.86368 7.62598 61.86368 4.86977Q61.86368 2.11357 60.66691 -0.3525Q59.47013 -2.81858 57.07659 -4.34174Q54.68305 -5.86491 51.41912 -5.86491Q47.9376 -5.86491 45.61659 -4.23295Q43.29558 -2.60098 42.28014 -0.20744L32.5609 -0.20744L37.13039 -30.88832L67.81127 -30.88832L67.81127 -22.25706L43.94836 -22.25706L41.99001 -8.98377L42.28014 -8.98377Q43.94836 -10.72453 46.34191 -12.10263Q48.73545 -13.48073 53.37748 -13.48073Q59.03494 -13.48073 63.13298 -10.94212Q67.23102 -8.40351 69.3707 -4.23295Q71.51038 -0.06238 71.51038 4.72471Q71.51038 9.58433 69.22564 13.8637Q66.94089 18.14306 62.3714 20.82673Q57.80191 23.5104 51.34659 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M116.54866 2.98395L125.25245 2.98395L125.25245 11.32509L116.54866 11.32509L116.54866 22.78509L106.75689 22.78509L106.75689 11.32509L80.71803 11.25256L80.71803 2.62129L104.43588 -30.88832L116.54866 -30.88832ZM106.75689 -20.08111L90.43727 2.98395L106.75689 2.98395Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M154.98655 23.5104Q148.16858 23.5104 143.74415 20.82673Q139.31972 18.14306 137.3251 14.22635Q135.33048 10.30965 135.33048 6.46547L144.61453 6.46547Q144.61453 8.78648 145.77504 10.81737Q146.93554 12.84825 149.29282 14.08129Q151.6501 15.31433 154.91402 15.31433Q159.19339 15.31433 162.02212 13.06585Q164.85086 10.81737 164.85086 6.97319Q164.85086 3.41914 162.38478 1.27946Q159.91871 -0.86023 155.71187 -0.86023L148.45871 -0.86023L148.45871 -8.47605L155.7844 -8.47605Q159.41098 -8.47605 161.732 -10.50693Q164.05301 -12.53782 164.05301 -15.72921Q164.05301 -19.1382 161.4056 -21.31415Q158.7582 -23.49009 154.91402 -23.49009Q151.94023 -23.49009 149.80054 -22.18453Q147.66086 -20.87896 146.53662 -18.77554Q145.41238 -16.67212 145.41238 -14.35111L136.34592 -14.35111Q136.34592 -18.19529 138.30428 -22.18453Q140.26263 -26.17376 144.46947 -28.8937Q148.6763 -31.61364 155.05909 -31.61364Q159.91871 -31.61364 164.16181 -29.80035Q168.40491 -27.98706 171.01605 -24.57807Q173.62718 -21.16908 173.62718 -16.67212Q173.62718 -12.24769 171.23364 -9.20136Q168.8401 -6.15503 165.50364 -4.99453L165.50364 -4.55934Q169.56541 -3.18124 172.10402 -0.09864Q174.64263 2.98395 174.64263 7.48091Q174.64263 12.41306 171.95896 16.03965Q169.27529 19.66623 164.77832 21.58832Q160.28136 23.5104 154.98655 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M192.55407 23.5104Q190.08799 23.5104 188.4923 21.84218Q186.8966 20.17395 186.8966 17.7804Q186.8966 15.38686 188.4923 13.7549Q190.08799 12.12294 192.55407 12.12294Q195.02015 12.12294 196.65211 13.7549Q198.28407 15.38686 198.28407 17.7804Q198.28407 20.17395 196.65211 21.84218Q195.02015 23.5104 192.55407 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M208.65222 -30.88832L246.94892 -30.88832L246.94892 -23.78022Q239.11551 -15.36655 233.45804 -3.14497Q227.80057 9.07661 227.29285 22.78509L217.42855 22.78509Q218.0088 8.1337 223.88387 -3.50763Q229.75893 -15.14896 235.99665 -22.25706L208.65222 -22.25706Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M273.20151 23.43787Q266.45606 23.43787 261.48765 19.73876Q256.51923 16.03965 253.94436 9.76566Q251.36948 3.49167 251.36948 -4.12415Q251.36948 -11.73997 253.94436 -18.01396Q256.51923 -24.28794 261.48765 -27.98706Q266.45606 -31.68617 273.20151 -31.68617Q279.94695 -31.68617 284.91536 -27.98706Q289.88378 -24.28794 292.45865 -18.01396Q295.03353 -11.73997 295.03353 -4.12415Q295.03353 3.49167 292.45865 9.76566Q289.88378 16.03965 284.91536 19.73876Q279.94695 23.43787 273.20151 23.43787ZM273.20151 14.66154Q278.42378 14.66154 281.65144 9.80192Q284.8791 4.94231 284.8791 -4.12415Q284.8791 -13.11807 281.65144 -17.94142Q278.42378 -22.76478 273.20151 -22.76478Q267.97923 -22.76478 264.75157 -17.94142Q261.52391 -13.11807 261.52391 -4.12415Q261.52391 4.94231 264.7153 9.80192Q267.9067 14.66154 273.20151 14.66154Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M324.11484 23.5104Q316.86168 23.5104 312.43725 20.42781Q308.01282 17.34521 306.16326 13.42851Q304.31371 9.5118 304.24117 6.538L313.45269 6.538Q313.88788 10.23711 316.49902 12.81199Q319.11016 15.38686 323.96978 15.38686Q327.37877 15.38686 329.80858 13.89996Q332.23839 12.41306 333.43516 10.01952Q334.63193 7.62598 334.63193 4.86977Q334.63193 2.11357 333.43516 -0.3525Q332.23839 -2.81858 329.84484 -4.34174Q327.4513 -5.86491 324.18737 -5.86491Q320.70586 -5.86491 318.38484 -4.23295Q316.06383 -2.60098 315.04839 -0.20744L305.32915 -0.20744L309.89864 -30.88832L340.57952 -30.88832L340.57952 -22.25706L316.71662 -22.25706L314.75826 -8.98377L315.04839 -8.98377Q316.71662 -10.72453 319.11016 -12.10263Q321.5037 -13.48073 326.14573 -13.48073Q331.8032 -13.48073 335.90123 -10.94212Q339.99927 -8.40351 342.13895 -4.23295Q344.27864 -0.06238 344.27864 4.72471Q344.27864 9.58433 341.99389 13.8637Q339.70914 18.14306 335.13965 20.82673Q330.57016 23.5104 324.11484 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M361.60983 23.5104Q359.14375 23.5104 357.54805 21.84218Q355.95236 20.17395 355.95236 17.7804Q355.95236 15.38686 357.54805 13.7549Q359.14375 12.12294 361.60983 12.12294Q364.0759 12.12294 365.70786 13.7549Q367.33983 15.38686 367.33983 17.7804Q367.33983 20.17395 365.70786 21.84218Q364.0759 23.5104 361.60983 23.5104Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M397.65418 -30.88832L397.65418 22.78509L387.93494 22.78509L387.93494 -16.67212L377.70798 -16.67212L377.70798 -22.54718L379.37621 -23.0549Q383.65557 -24.28794 386.33924 -25.88364Q389.02291 -27.47933 391.41646 -30.23554L392.06924 -30.88832Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/><path d="M431.30499 23.43787Q424.55954 23.43787 419.59113 19.73876Q414.62271 16.03965 412.04784 9.76566Q409.47296 3.49167 409.47296 -4.12415Q409.47296 -11.73997 412.04784 -18.01396Q414.62271 -24.28794 419.59113 -27.98706Q424.55954 -31.68617 431.30499 -31.68617Q438.05043 -31.68617 443.01884 -27.98706Q447.98726 -24.28794 450.56213 -18.01396Q453.13701 -11.73997 453.13701 -4.12415Q453.13701 3.49167 450.56213 9.76566Q447.98726 16.03965 443.01884 19.73876Q438.05043 23.43787 431.30499 23.43787ZM431.30499 14.66154Q436.52726 14.66154 439.75492 9.80192Q442.98258 4.94231 442.98258 -4.12415Q442.98258 -13.11807 439.75492 -17.94142Q436.52726 -22.76478 431.30499 -22.76478Q426.08271 -22.76478 422.85505 -17.94142Q419.62739 -13.11807 419.62739 -4.12415Q419.62739 4.94231 422.81878 9.80192Q426.01018 14.66154 431.30499 14.66154Z" fill="rgb(219, 32, 32)" transform="matrix(1 0 0 1 487.029814 70.964553)"/></g></g>

  </svg>