<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1714px" height="1871px" viewBox="0 0 1713.9919344799928 1871.4494225000008">
    <g id="d4d8bc88-881c-4c05-8073-e3cf07638a72">
<g style="">
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_347)">
<clipPath id="CLIPPATH_347">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -540.655) matrix(1 0 0 1 173.41 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_348)">
<clipPath id="CLIPPATH_348">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -540.655) matrix(1 0 0 1 55.8 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_349)">
<clipPath id="CLIPPATH_349">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.52734 -441.998532) matrix(0.98708 0.160226 -0.160226 0.98708 216.428894 482.91002)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.52734 -441.998532)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_350)">
<clipPath id="CLIPPATH_350">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -441.985) matrix(1 0 0 1 173.41 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_351)">
<clipPath id="CLIPPATH_351">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -441.985) matrix(1 0 0 1 55.8 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_352)">
<clipPath id="CLIPPATH_352">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -343.325) matrix(1 0 0 1 408.62 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_353)">
<clipPath id="CLIPPATH_353">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.519467 -343.34318) matrix(0.98708 0.160226 -0.160226 0.98708 232.228251 385.527995)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.519467 -343.34318)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_354)">
<clipPath id="CLIPPATH_354">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -343.325) matrix(1 0 0 1 173.41 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_355)">
<clipPath id="CLIPPATH_355">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -343.325) matrix(1 0 0 1 55.8 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_356)">
<clipPath id="CLIPPATH_356">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -244.655) matrix(1 0 0 1 408.62 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_357)">
<clipPath id="CLIPPATH_357">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 -291.536222 -244.654608) matrix(0.382683 0.92388 -0.92388 0.382683 -114.659679 362.487283)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 -291.536222 -244.654608)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_358)">
<clipPath id="CLIPPATH_358">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -244.655) matrix(1 0 0 1 173.41 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_359)">
<clipPath id="CLIPPATH_359">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -244.655) matrix(1 0 0 1 55.8 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_360)">
<clipPath id="CLIPPATH_360">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -145.985) matrix(1 0 0 1 408.62 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_361)">
<clipPath id="CLIPPATH_361">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.510517 -146.002735) matrix(0.98708 0.160226 -0.160226 0.98708 263.838438 190.735676)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.510517 -146.002735)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_362)">
<clipPath id="CLIPPATH_362">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -145.985) matrix(1 0 0 1 173.41 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_363)">
<clipPath id="CLIPPATH_363">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -145.985) matrix(1 0 0 1 55.8 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_364)">
<clipPath id="CLIPPATH_364">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 -145.985) matrix(1 0 0 1 526.23 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_365)">
<clipPath id="CLIPPATH_365">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -47.325) matrix(1 0 0 1 408.62 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_366)">
<clipPath id="CLIPPATH_366">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.512644 -47.347383) matrix(0.98708 0.160226 -0.160226 0.98708 279.647666 93.355253)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.512644 -47.347383)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_367)">
<clipPath id="CLIPPATH_367">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -47.325) matrix(1 0 0 1 173.41 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_368)">
<clipPath id="CLIPPATH_368">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -47.325) matrix(1 0 0 1 55.8 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_369)">
<clipPath id="CLIPPATH_369">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 -47.325) matrix(1 0 0 1 526.23 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_370)">
<clipPath id="CLIPPATH_370">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 47.315) matrix(1 0 0 1 408.62 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_371)">
<clipPath id="CLIPPATH_371">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.508879 47.299906) matrix(0.98708 0.160226 -0.160226 0.98708 294.808882 -0.069833)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.508879 47.299906)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_372)">
<clipPath id="CLIPPATH_372">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 47.315) matrix(1 0 0 1 173.41 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_373)">
<clipPath id="CLIPPATH_373">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 47.315) matrix(1 0 0 1 55.8 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_374)">
<clipPath id="CLIPPATH_374">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 47.315) matrix(1 0 0 1 526.23 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_375)">
<clipPath id="CLIPPATH_375">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 145.985) matrix(1 0 0 1 408.62 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_376)">
<clipPath id="CLIPPATH_376">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 -291.531922 145.986848) matrix(0.382683 0.92388 -0.92388 0.382683 246.244321 212.991297)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 -291.531922 145.986848)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_377)">
<clipPath id="CLIPPATH_377">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 145.985) matrix(1 0 0 1 173.41 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_378)">
<clipPath id="CLIPPATH_378">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 145.985) matrix(1 0 0 1 55.8 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_379)">
<clipPath id="CLIPPATH_379">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 145.985) matrix(1 0 0 1 526.23 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -526.75 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_380)">
<clipPath id="CLIPPATH_380">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 244.655) matrix(1 0 0 1 408.62 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_381)">
<clipPath id="CLIPPATH_381">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.055299 -0.99847 0.99847 0.055299 -291.532865 244.643884) matrix(0.055299 0.99847 -0.99847 0.055299 260.367174 277.038804)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.055299 -0.99847 0.99847 0.055299 -291.532865 244.643884)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_382)">
<clipPath id="CLIPPATH_382">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 244.655) matrix(1 0 0 1 173.41 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_383)">
<clipPath id="CLIPPATH_383">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 244.655) matrix(1 0 0 1 55.8 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_384)">
<clipPath id="CLIPPATH_384">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 343.315) matrix(1 0 0 1 408.62 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -409.14 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_385)">
<clipPath id="CLIPPATH_385">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.492056 343.295703) matrix(0.98708 0.160226 -0.160226 0.98708 342.218426 -292.244177)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 -291.492056 343.295703)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_386)">
<clipPath id="CLIPPATH_386">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 343.315) matrix(1 0 0 1 173.41 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_387)">
<clipPath id="CLIPPATH_387">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 343.315) matrix(1 0 0 1 55.8 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_388)">
<clipPath id="CLIPPATH_388">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 -291.53358 441.981144) matrix(0.382683 0.92388 -0.92388 0.382683 519.708028 99.720716)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 -291.53358 441.981144)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_389)">
<clipPath id="CLIPPATH_389">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 441.985) matrix(1 0 0 1 173.41 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_390)">
<clipPath id="CLIPPATH_390">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 441.985) matrix(1 0 0 1 55.8 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_391)">
<clipPath id="CLIPPATH_391">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 540.655) matrix(1 0 0 1 173.41 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -173.93 540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_392)">
<clipPath id="CLIPPATH_392">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 540.655) matrix(1 0 0 1 55.8 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 -56.32 540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_393)">
<clipPath id="CLIPPATH_393">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -540.655) matrix(1 0 0 1 -56.84 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_394)">
<clipPath id="CLIPPATH_394">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -540.655) matrix(1 0 0 1 -174.45 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_395)">
<clipPath id="CLIPPATH_395">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -441.985) matrix(1 0 0 1 -56.84 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_396)">
<clipPath id="CLIPPATH_396">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -441.985) matrix(1 0 0 1 -174.45 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_397)">
<clipPath id="CLIPPATH_397">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.545858 -441.985968) matrix(0.382683 0.92388 -0.92388 0.382683 -520.105936 -100.694874)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.545858 -441.985968)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_398)">
<clipPath id="CLIPPATH_398">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -343.325) matrix(1 0 0 1 -56.84 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_399)">
<clipPath id="CLIPPATH_399">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -343.325) matrix(1 0 0 1 -174.45 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_400)">
<clipPath id="CLIPPATH_400">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.535813 -343.320421) matrix(0.382683 0.92388 -0.92388 0.382683 -428.947012 -138.443264)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.535813 -343.320421)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_401)">
<clipPath id="CLIPPATH_401">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -343.325) matrix(1 0 0 1 -409.67 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_402)">
<clipPath id="CLIPPATH_402">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -244.655) matrix(1 0 0 1 -56.84 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_403)">
<clipPath id="CLIPPATH_403">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -244.655) matrix(1 0 0 1 -174.45 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_404)">
<clipPath id="CLIPPATH_404">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 291.546974 -244.700787) matrix(0.98708 0.160226 -0.160226 0.98708 -327.500151 194.737763)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 291.546974 -244.700787)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_405)">
<clipPath id="CLIPPATH_405">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -244.655) matrix(1 0 0 1 -409.67 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_406)">
<clipPath id="CLIPPATH_406">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -145.985) matrix(1 0 0 1 -56.84 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_407)">
<clipPath id="CLIPPATH_407">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -145.985) matrix(1 0 0 1 -174.45 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_408)">
<clipPath id="CLIPPATH_408">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.5442 -145.991672) matrix(0.382683 0.92388 -0.92388 0.382683 -246.642229 -213.965455)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.5442 -145.991672)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_409)">
<clipPath id="CLIPPATH_409">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -145.985) matrix(1 0 0 1 -409.67 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_410)">
<clipPath id="CLIPPATH_410">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 -145.985) matrix(1 0 0 1 -527.27 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_411)">
<clipPath id="CLIPPATH_411">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -47.325) matrix(1 0 0 1 -56.84 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_412)">
<clipPath id="CLIPPATH_412">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -47.325) matrix(1 0 0 1 -174.45 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_413)">
<clipPath id="CLIPPATH_413">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.534155 -47.326125) matrix(0.382683 0.92388 -0.92388 0.382683 -155.483305 -251.713845)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.534155 -47.326125)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_414)">
<clipPath id="CLIPPATH_414">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -47.325) matrix(1 0 0 1 -409.67 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_415)">
<clipPath id="CLIPPATH_415">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 -47.325) matrix(1 0 0 1 -527.27 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_416)">
<clipPath id="CLIPPATH_416">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 47.315) matrix(1 0 0 1 -56.84 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_417)">
<clipPath id="CLIPPATH_417">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 47.315) matrix(1 0 0 1 -174.45 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_418)">
<clipPath id="CLIPPATH_418">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.530114 47.311035) matrix(0.382683 0.92388 -0.92388 0.382683 -68.048424 -287.926185)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.530114 47.311035)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_419)">
<clipPath id="CLIPPATH_419">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 47.315) matrix(1 0 0 1 -409.67 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_420)">
<clipPath id="CLIPPATH_420">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 47.315) matrix(1 0 0 1 -527.27 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_421)">
<clipPath id="CLIPPATH_421">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 145.985) matrix(1 0 0 1 -56.84 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_422)">
<clipPath id="CLIPPATH_422">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 145.985) matrix(1 0 0 1 -174.45 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_423)">
<clipPath id="CLIPPATH_423">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 291.567562 145.942298) matrix(0.98708 0.160226 -0.160226 0.98708 -264.929391 -190.861667)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 291.567562 145.942298)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_424)">
<clipPath id="CLIPPATH_424">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 145.985) matrix(1 0 0 1 -409.67 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_425)">
<clipPath id="CLIPPATH_425">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 145.985) matrix(1 0 0 1 -527.27 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 526.75 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_426)">
<clipPath id="CLIPPATH_426">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 244.655) matrix(1 0 0 1 -56.84 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_427)">
<clipPath id="CLIPPATH_427">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 244.655) matrix(1 0 0 1 -174.45 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_428)">
<clipPath id="CLIPPATH_428">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.998971 -0.045363 0.045363 0.998971 291.574873 244.605515) matrix(0.998971 0.045363 -0.045363 0.998971 -280.697918 -257.609002)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.998971 -0.045363 0.045363 0.998971 291.574873 244.605515)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_429)">
<clipPath id="CLIPPATH_429">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 244.655) matrix(1 0 0 1 -409.67 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_430)">
<clipPath id="CLIPPATH_430">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 343.315) matrix(1 0 0 1 -56.84 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_431)">
<clipPath id="CLIPPATH_431">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 343.315) matrix(1 0 0 1 -174.45 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_432)">
<clipPath id="CLIPPATH_432">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.538455 343.315331) matrix(0.382683 0.92388 -0.92388 0.382683 205.420695 -401.209831)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.382683 -0.92388 0.92388 0.382683 291.538455 343.315331)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_433)">
<clipPath id="CLIPPATH_433">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 343.315) matrix(1 0 0 1 -409.67 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 409.15 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_434)">
<clipPath id="CLIPPATH_434">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 441.985) matrix(1 0 0 1 -56.84 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_435)">
<clipPath id="CLIPPATH_435">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 441.985) matrix(1 0 0 1 -174.45 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_436)">
<clipPath id="CLIPPATH_436">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 291.574386 441.938096) matrix(0.98708 0.160226 -0.160226 0.98708 -217.509976 -483.034409)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(0.98708 -0.160226 0.160226 0.98708 291.574386 441.938096)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_437)">
<clipPath id="CLIPPATH_437">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 540.655) matrix(1 0 0 1 -56.84 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 56.32 540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_438)">
<clipPath id="CLIPPATH_438">
	<circle transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 540.655) matrix(1 0 0 1 -174.45 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 1242.549134 483.212577) matrix(1 0 0 1 173.93 540.655)"/>
</g>
</g>
</g>
<g id="404c7e19-2371-4bdb-8aad-dd21ec5f172f">
<g style="">
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_439)">
<clipPath id="CLIPPATH_439">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -540.655) matrix(1 0 0 1 173.41 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_440)">
<clipPath id="CLIPPATH_440">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -540.655) matrix(1 0 0 1 55.8 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_441)">
<clipPath id="CLIPPATH_441">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.52734 -441.998532) matrix(0.98708 0.160226 -0.160226 0.98708 216.428894 482.91002)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.52734 -441.998532)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_442)">
<clipPath id="CLIPPATH_442">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -441.985) matrix(1 0 0 1 173.41 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_443)">
<clipPath id="CLIPPATH_443">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -441.985) matrix(1 0 0 1 55.8 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_444)">
<clipPath id="CLIPPATH_444">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -343.325) matrix(1 0 0 1 408.62 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_445)">
<clipPath id="CLIPPATH_445">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.519467 -343.34318) matrix(0.98708 0.160226 -0.160226 0.98708 232.228251 385.527995)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.519467 -343.34318)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_446)">
<clipPath id="CLIPPATH_446">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -343.325) matrix(1 0 0 1 173.41 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_447)">
<clipPath id="CLIPPATH_447">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -343.325) matrix(1 0 0 1 55.8 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_448)">
<clipPath id="CLIPPATH_448">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -244.655) matrix(1 0 0 1 408.62 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_449)">
<clipPath id="CLIPPATH_449">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 -291.536222 -244.654608) matrix(0.382683 0.92388 -0.92388 0.382683 -114.659679 362.487283)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 -291.536222 -244.654608)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_450)">
<clipPath id="CLIPPATH_450">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -244.655) matrix(1 0 0 1 173.41 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_451)">
<clipPath id="CLIPPATH_451">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -244.655) matrix(1 0 0 1 55.8 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_452)">
<clipPath id="CLIPPATH_452">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -145.985) matrix(1 0 0 1 408.62 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_453)">
<clipPath id="CLIPPATH_453">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.510517 -146.002735) matrix(0.98708 0.160226 -0.160226 0.98708 263.838438 190.735676)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.510517 -146.002735)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_454)">
<clipPath id="CLIPPATH_454">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -145.985) matrix(1 0 0 1 173.41 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_455)">
<clipPath id="CLIPPATH_455">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -145.985) matrix(1 0 0 1 55.8 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_456)">
<clipPath id="CLIPPATH_456">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 -145.985) matrix(1 0 0 1 526.23 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_457)">
<clipPath id="CLIPPATH_457">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -47.325) matrix(1 0 0 1 408.62 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_458)">
<clipPath id="CLIPPATH_458">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.512644 -47.347383) matrix(0.98708 0.160226 -0.160226 0.98708 279.647666 93.355253)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.512644 -47.347383)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_459)">
<clipPath id="CLIPPATH_459">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -47.325) matrix(1 0 0 1 173.41 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_460)">
<clipPath id="CLIPPATH_460">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -47.325) matrix(1 0 0 1 55.8 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_461)">
<clipPath id="CLIPPATH_461">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 -47.325) matrix(1 0 0 1 526.23 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_462)">
<clipPath id="CLIPPATH_462">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 47.315) matrix(1 0 0 1 408.62 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_463)">
<clipPath id="CLIPPATH_463">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.508879 47.299906) matrix(0.98708 0.160226 -0.160226 0.98708 294.808882 -0.069833)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.508879 47.299906)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_464)">
<clipPath id="CLIPPATH_464">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 47.315) matrix(1 0 0 1 173.41 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_465)">
<clipPath id="CLIPPATH_465">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 47.315) matrix(1 0 0 1 55.8 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_466)">
<clipPath id="CLIPPATH_466">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 47.315) matrix(1 0 0 1 526.23 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_467)">
<clipPath id="CLIPPATH_467">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 145.985) matrix(1 0 0 1 408.62 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_468)">
<clipPath id="CLIPPATH_468">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 -291.531922 145.986848) matrix(0.382683 0.92388 -0.92388 0.382683 246.244321 212.991297)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 -291.531922 145.986848)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_469)">
<clipPath id="CLIPPATH_469">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 145.985) matrix(1 0 0 1 173.41 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_470)">
<clipPath id="CLIPPATH_470">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 145.985) matrix(1 0 0 1 55.8 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_471)">
<clipPath id="CLIPPATH_471">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 145.985) matrix(1 0 0 1 526.23 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -526.75 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_472)">
<clipPath id="CLIPPATH_472">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 244.655) matrix(1 0 0 1 408.62 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_473)">
<clipPath id="CLIPPATH_473">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.055299 -0.99847 0.99847 0.055299 -291.532865 244.643884) matrix(0.055299 0.99847 -0.99847 0.055299 260.367174 277.038804)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.055299 -0.99847 0.99847 0.055299 -291.532865 244.643884)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_474)">
<clipPath id="CLIPPATH_474">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 244.655) matrix(1 0 0 1 173.41 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_475)">
<clipPath id="CLIPPATH_475">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 244.655) matrix(1 0 0 1 55.8 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_476)">
<clipPath id="CLIPPATH_476">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 343.315) matrix(1 0 0 1 408.62 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -409.14 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_477)">
<clipPath id="CLIPPATH_477">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.492056 343.295703) matrix(0.98708 0.160226 -0.160226 0.98708 342.218426 -292.244177)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 -291.492056 343.295703)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_478)">
<clipPath id="CLIPPATH_478">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 343.315) matrix(1 0 0 1 173.41 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_479)">
<clipPath id="CLIPPATH_479">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 343.315) matrix(1 0 0 1 55.8 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_480)">
<clipPath id="CLIPPATH_480">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 -291.53358 441.981144) matrix(0.382683 0.92388 -0.92388 0.382683 519.708028 99.720716)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 -291.53358 441.981144)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_481)">
<clipPath id="CLIPPATH_481">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 441.985) matrix(1 0 0 1 173.41 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_482)">
<clipPath id="CLIPPATH_482">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 441.985) matrix(1 0 0 1 55.8 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_483)">
<clipPath id="CLIPPATH_483">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 540.655) matrix(1 0 0 1 173.41 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -173.93 540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_484)">
<clipPath id="CLIPPATH_484">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 540.655) matrix(1 0 0 1 55.8 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 -56.32 540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_485)">
<clipPath id="CLIPPATH_485">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -540.655) matrix(1 0 0 1 -56.84 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_486)">
<clipPath id="CLIPPATH_486">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -540.655) matrix(1 0 0 1 -174.45 540.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_487)">
<clipPath id="CLIPPATH_487">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -441.985) matrix(1 0 0 1 -56.84 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_488)">
<clipPath id="CLIPPATH_488">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -441.985) matrix(1 0 0 1 -174.45 441.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_489)">
<clipPath id="CLIPPATH_489">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.545858 -441.985968) matrix(0.382683 0.92388 -0.92388 0.382683 -520.105936 -100.694874)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.545858 -441.985968)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_490)">
<clipPath id="CLIPPATH_490">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -343.325) matrix(1 0 0 1 -56.84 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_491)">
<clipPath id="CLIPPATH_491">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -343.325) matrix(1 0 0 1 -174.45 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_492)">
<clipPath id="CLIPPATH_492">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.535813 -343.320421) matrix(0.382683 0.92388 -0.92388 0.382683 -428.947012 -138.443264)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.535813 -343.320421)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_493)">
<clipPath id="CLIPPATH_493">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -343.325) matrix(1 0 0 1 -409.67 343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -343.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_494)">
<clipPath id="CLIPPATH_494">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -244.655) matrix(1 0 0 1 -56.84 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_495)">
<clipPath id="CLIPPATH_495">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -244.655) matrix(1 0 0 1 -174.45 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_496)">
<clipPath id="CLIPPATH_496">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 291.546974 -244.700787) matrix(0.98708 0.160226 -0.160226 0.98708 -327.500151 194.737763)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 291.546974 -244.700787)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_497)">
<clipPath id="CLIPPATH_497">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -244.655) matrix(1 0 0 1 -409.67 244.65)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_498)">
<clipPath id="CLIPPATH_498">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -145.985) matrix(1 0 0 1 -56.84 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_499)">
<clipPath id="CLIPPATH_499">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -145.985) matrix(1 0 0 1 -174.45 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_500)">
<clipPath id="CLIPPATH_500">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.5442 -145.991672) matrix(0.382683 0.92388 -0.92388 0.382683 -246.642229 -213.965455)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.5442 -145.991672)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_501)">
<clipPath id="CLIPPATH_501">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -145.985) matrix(1 0 0 1 -409.67 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_502)">
<clipPath id="CLIPPATH_502">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 -145.985) matrix(1 0 0 1 -527.27 145.98)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 -145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_503)">
<clipPath id="CLIPPATH_503">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -47.325) matrix(1 0 0 1 -56.84 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_504)">
<clipPath id="CLIPPATH_504">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -47.325) matrix(1 0 0 1 -174.45 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_505)">
<clipPath id="CLIPPATH_505">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.534155 -47.326125) matrix(0.382683 0.92388 -0.92388 0.382683 -155.483305 -251.713845)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.534155 -47.326125)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_506)">
<clipPath id="CLIPPATH_506">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -47.325) matrix(1 0 0 1 -409.67 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_507)">
<clipPath id="CLIPPATH_507">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 -47.325) matrix(1 0 0 1 -527.27 47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 -47.325)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_508)">
<clipPath id="CLIPPATH_508">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 47.315) matrix(1 0 0 1 -56.84 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_509)">
<clipPath id="CLIPPATH_509">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 47.315) matrix(1 0 0 1 -174.45 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_510)">
<clipPath id="CLIPPATH_510">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.530114 47.311035) matrix(0.382683 0.92388 -0.92388 0.382683 -68.048424 -287.926185)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.530114 47.311035)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_511)">
<clipPath id="CLIPPATH_511">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 47.315) matrix(1 0 0 1 -409.67 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_512)">
<clipPath id="CLIPPATH_512">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 47.315) matrix(1 0 0 1 -527.27 -47.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 47.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_513)">
<clipPath id="CLIPPATH_513">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 145.985) matrix(1 0 0 1 -56.84 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_514)">
<clipPath id="CLIPPATH_514">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 145.985) matrix(1 0 0 1 -174.45 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_515)">
<clipPath id="CLIPPATH_515">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 291.567562 145.942298) matrix(0.98708 0.160226 -0.160226 0.98708 -264.929391 -190.861667)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 291.567562 145.942298)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_516)">
<clipPath id="CLIPPATH_516">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 145.985) matrix(1 0 0 1 -409.67 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_517)">
<clipPath id="CLIPPATH_517">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 145.985) matrix(1 0 0 1 -527.27 -145.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 526.75 145.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_518)">
<clipPath id="CLIPPATH_518">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 244.655) matrix(1 0 0 1 -56.84 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_519)">
<clipPath id="CLIPPATH_519">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 244.655) matrix(1 0 0 1 -174.45 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_520)">
<clipPath id="CLIPPATH_520">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.998971 -0.045363 0.045363 0.998971 291.574873 244.605515) matrix(0.998971 0.045363 -0.045363 0.998971 -280.697918 -257.609002)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.998971 -0.045363 0.045363 0.998971 291.574873 244.605515)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_521)">
<clipPath id="CLIPPATH_521">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 244.655) matrix(1 0 0 1 -409.67 -244.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 244.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_522)">
<clipPath id="CLIPPATH_522">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 343.315) matrix(1 0 0 1 -56.84 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_523)">
<clipPath id="CLIPPATH_523">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 343.315) matrix(1 0 0 1 -174.45 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_524)">
<clipPath id="CLIPPATH_524">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.538455 343.315331) matrix(0.382683 0.92388 -0.92388 0.382683 205.420695 -401.209831)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.382683 -0.92388 0.92388 0.382683 291.538455 343.315331)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_525)">
<clipPath id="CLIPPATH_525">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 343.315) matrix(1 0 0 1 -409.67 -343.32)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 409.15 343.315)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_526)">
<clipPath id="CLIPPATH_526">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 441.985) matrix(1 0 0 1 -56.84 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_527)">
<clipPath id="CLIPPATH_527">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 441.985) matrix(1 0 0 1 -174.45 -441.99)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 441.985)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_528)">
<clipPath id="CLIPPATH_528">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 291.574386 441.938096) matrix(0.98708 0.160226 -0.160226 0.98708 -217.509976 -483.034409)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(0.98708 -0.160226 0.160226 0.98708 291.574386 441.938096)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_529)">
<clipPath id="CLIPPATH_529">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 540.655) matrix(1 0 0 1 -56.84 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 56.32 540.655)"/>
</g>
		<g id="Layer_12_Image" clip-path="url(#CLIPPATH_530)">
<clipPath id="CLIPPATH_530">
	<circle transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 540.655) matrix(1 0 0 1 -174.45 -540.66)" id="clippath" cx="0" cy="0" r="608.05"/>
</clipPath>
<circle style="stroke: rgb(193,193,193); stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(170,30,183); fill-rule: nonzero; opacity: 1;" cx="0" cy="0" r="30.22" transform="matrix(0.846442 0 0 0.846442 471.442801 1388.236846) matrix(1 0 0 1 173.93 540.655)"/>
</g>
</g>
</g>

  </svg>